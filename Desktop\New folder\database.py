from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

class Product(db.Model):
    __tablename__ = 'Products'
    ProductID = db.Column(db.Integer, primary_key=True, autoincrement=True)
    Barcode = db.Column(db.String(100), unique=True, nullable=False)
    Name = db.Column(db.String(100), nullable=False)
    Category = db.Column(db.String(50))
    Price = db.Column(db.Float, nullable=False)
    StockQty = db.Column(db.Integer, nullable=False, default=0)
    CreatedAt = db.Column(db.DateTime, default=db.func.current_timestamp())

from flask_login import UserMixin

class User(UserMixin, db.Model):
    __tablename__ = 'Users'
    UserID = db.Column(db.Integer, primary_key=True, autoincrement=True)
    Username = db.Column(db.String(50), unique=True, nullable=False)
    Password = db.Column(db.String(100), nullable=False)
    Role = db.Column(db.String(10), nullable=False)
    CreatedAt = db.Column(db.DateTime, default=db.func.current_timestamp())
    
    def get_id(self):
        return str(self.UserID)

class Sale(db.Model):
    __tablename__ = 'Sales'
    SaleID = db.Column(db.Integer, primary_key=True, autoincrement=True)
    UserID = db.Column(db.Integer, db.ForeignKey('Users.UserID'), nullable=False)
    DateTime = db.Column(db.DateTime, default=db.func.current_timestamp())
    TotalAmount = db.Column(db.Float, nullable=False)
    Discount = db.Column(db.Float, default=0)
    Tax = db.Column(db.Float, default=0)
    NetAmount = db.Column(db.Float, nullable=False)
    user = db.relationship('User', backref='sales')

class SaleItem(db.Model):
    __tablename__ = 'SaleItems'
    SaleItemID = db.Column(db.Integer, primary_key=True, autoincrement=True)
    SaleID = db.Column(db.Integer, db.ForeignKey('Sales.SaleID', ondelete='CASCADE'), nullable=False)
    ProductID = db.Column(db.Integer, db.ForeignKey('Products.ProductID'), nullable=False)
    Quantity = db.Column(db.Integer, nullable=False)
    UnitPrice = db.Column(db.Float, nullable=False)
    SubTotal = db.Column(db.Float, nullable=False)
    sale = db.relationship('Sale', backref='items')
    product = db.relationship('Product')

class Refund(db.Model):
    __tablename__ = 'Refunds'
    RefundID = db.Column(db.Integer, primary_key=True, autoincrement=True)
    SaleID = db.Column(db.Integer, db.ForeignKey('Sales.SaleID'), nullable=False)
    ProductID = db.Column(db.Integer, db.ForeignKey('Products.ProductID'), nullable=False)
    Quantity = db.Column(db.Integer, nullable=False)
    RefundAmount = db.Column(db.Float, nullable=False)
    DateTime = db.Column(db.DateTime, default=db.func.current_timestamp())
    sale = db.relationship('Sale')
    product = db.relationship('Product')